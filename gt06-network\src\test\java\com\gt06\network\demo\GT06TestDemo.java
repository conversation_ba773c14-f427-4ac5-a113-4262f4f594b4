package com.gt06.network.demo;

import com.gt06.network.client.DeviceSimulator;
import com.gt06.network.client.GT06TestClient;
import com.gt06.protocol.message.AlarmMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.Scanner;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * GT06测试演示工具
 * 提供交互式命令行界面，用于手动测试GT06服务器功能
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
public class GT06TestDemo {
    
    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 8888;
    private static final String DEFAULT_DEVICE_IMEI = "123456789012345";
    
    private GT06TestClient testClient;
    private DeviceSimulator deviceSimulator;
    private ExecutorService executor;
    private Scanner scanner;
    
    public static void main(String[] args) {
        GT06TestDemo demo = new GT06TestDemo();
        demo.run();
    }
    
    public void run() {
        log.info("🚀 GT06 Test Demo Tool Starting...");
        log.info("=".repeat(50));
        
        scanner = new Scanner(System.in);
        executor = Executors.newCachedThreadPool();
        
        try {
            showWelcomeMessage();
            
            while (true) {
                showMainMenu();
                String choice = scanner.nextLine().trim();
                
                if (!handleMenuChoice(choice)) {
                    break;
                }
            }
            
        } catch (Exception e) {
            log.error("❌ Demo tool encountered error: {}", e.getMessage(), e);
        } finally {
            cleanup();
        }
        
        log.info("👋 GT06 Test Demo Tool Exited");
    }
    
    private void showWelcomeMessage() {
        System.out.println();
        System.out.println("🎯 GT06 Protocol Test Demo Tool");
        System.out.println("📡 Server: " + DEFAULT_HOST + ":" + DEFAULT_PORT);
        System.out.println("📱 Default Device IMEI: " + DEFAULT_DEVICE_IMEI);
        System.out.println("=".repeat(50));
    }
    
    private void showMainMenu() {
        System.out.println();
        System.out.println("📋 Main Menu:");
        System.out.println("1. 🔌 Connect Test Client");
        System.out.println("2. 💓 Send Heartbeat");
        System.out.println("3. 📍 Send Location");
        System.out.println("4. 🚨 Send Alarm");
        System.out.println("5. 🤖 Start Device Simulator");
        System.out.println("6. 🛑 Stop Device Simulator");
        System.out.println("7. 📊 Show Statistics");
        System.out.println("8. 🧪 Run Quick Test");
        System.out.println("9. 🔄 Disconnect Client");
        System.out.println("0. 🚪 Exit");
        System.out.print("👉 Please select an option: ");
    }
    
    private boolean handleMenuChoice(String choice) {
        try {
            switch (choice) {
                case "1":
                    connectTestClient();
                    break;
                case "2":
                    sendHeartbeat();
                    break;
                case "3":
                    sendLocation();
                    break;
                case "4":
                    sendAlarm();
                    break;
                case "5":
                    startDeviceSimulator();
                    break;
                case "6":
                    stopDeviceSimulator();
                    break;
                case "7":
                    showStatistics();
                    break;
                case "8":
                    runQuickTest();
                    break;
                case "9":
                    disconnectClient();
                    break;
                case "0":
                    return false;
                default:
                    System.out.println("❌ Invalid option, please try again");
            }
        } catch (Exception e) {
            log.error("❌ Error handling menu choice: {}", e.getMessage(), e);
            System.out.println("❌ Operation failed: " + e.getMessage());
        }
        
        return true;
    }
    
    private void connectTestClient() {
        if (testClient != null && testClient.isConnected()) {
            System.out.println("⚠️ Test client is already connected");
            return;
        }
        
        System.out.print("📡 Enter server host (default: " + DEFAULT_HOST + "): ");
        String host = scanner.nextLine().trim();
        if (host.isEmpty()) {
            host = DEFAULT_HOST;
        }
        
        System.out.print("🔌 Enter server port (default: " + DEFAULT_PORT + "): ");
        String portStr = scanner.nextLine().trim();
        int port = portStr.isEmpty() ? DEFAULT_PORT : Integer.parseInt(portStr);
        
        System.out.print("📱 Enter device IMEI (default: " + DEFAULT_DEVICE_IMEI + "): ");
        String imei = scanner.nextLine().trim();
        if (imei.isEmpty()) {
            imei = DEFAULT_DEVICE_IMEI;
        }
        
        System.out.println("🔌 Connecting to " + host + ":" + port + " with IMEI: " + imei);
        
        testClient = new GT06TestClient(host, port, imei);
        
        if (testClient.connect()) {
            System.out.println("✅ Connected successfully");
            
            if (testClient.sendLogin()) {
                System.out.println("✅ Login successful");
            } else {
                System.out.println("❌ Login failed");
            }
        } else {
            System.out.println("❌ Connection failed");
        }
    }
    
    private void sendHeartbeat() {
        if (!ensureClientConnected()) return;
        
        System.out.println("💓 Sending heartbeat...");
        
        if (testClient.sendHeartbeat()) {
            System.out.println("✅ Heartbeat sent successfully");
        } else {
            System.out.println("❌ Failed to send heartbeat");
        }
    }
    
    private void sendLocation() {
        if (!ensureClientConnected()) return;
        
        System.out.print("📍 Enter latitude (default: 39.9042): ");
        String latStr = scanner.nextLine().trim();
        double latitude = latStr.isEmpty() ? 39.9042 : Double.parseDouble(latStr);
        
        System.out.print("📍 Enter longitude (default: 116.4074): ");
        String lngStr = scanner.nextLine().trim();
        double longitude = lngStr.isEmpty() ? 116.4074 : Double.parseDouble(lngStr);
        
        System.out.print("🚗 Enter speed in km/h (default: 60): ");
        String speedStr = scanner.nextLine().trim();
        int speed = speedStr.isEmpty() ? 60 : Integer.parseInt(speedStr);
        
        System.out.println("📍 Sending location: " + latitude + ", " + longitude + " @ " + speed + "km/h");
        
        if (testClient.sendLocation(latitude, longitude, speed)) {
            System.out.println("✅ Location sent successfully");
        } else {
            System.out.println("❌ Failed to send location");
        }
    }
    
    private void sendAlarm() {
        if (!ensureClientConnected()) return;
        
        System.out.println("🚨 Select alarm type:");
        System.out.println("1. SOS Emergency");
        System.out.println("2. Overspeed");
        System.out.println("3. Power Off");
        System.out.println("4. Vibration");
        System.out.println("5. Tamper");
        System.out.print("👉 Select alarm type: ");
        
        String alarmChoice = scanner.nextLine().trim();
        AlarmMessage.AlarmType alarmType;
        
        switch (alarmChoice) {
            case "1":
                alarmType = AlarmMessage.AlarmType.SOS;
                break;
            case "2":
                alarmType = AlarmMessage.AlarmType.OVERSPEED;
                break;
            case "3":
                alarmType = AlarmMessage.AlarmType.POWER_OFF;
                break;
            case "4":
                alarmType = AlarmMessage.AlarmType.VIBRATION;
                break;
            case "5":
                alarmType = AlarmMessage.AlarmType.TAMPER;
                break;
            default:
                System.out.println("❌ Invalid alarm type");
                return;
        }
        
        System.out.println("🚨 Sending alarm: " + alarmType.getDescription());
        
        if (testClient.sendAlarm(alarmType)) {
            System.out.println("✅ Alarm sent successfully");
        } else {
            System.out.println("❌ Failed to send alarm");
        }
    }
    
    private void startDeviceSimulator() {
        if (deviceSimulator != null && deviceSimulator.isRunning()) {
            System.out.println("⚠️ Device simulator is already running");
            return;
        }
        
        System.out.print("📱 Enter device ID (default: SIM001): ");
        String deviceId = scanner.nextLine().trim();
        if (deviceId.isEmpty()) {
            deviceId = "SIM001";
        }
        
        System.out.println("🤖 Starting device simulator: " + deviceId);
        
        deviceSimulator = new DeviceSimulator(DEFAULT_HOST, DEFAULT_PORT, deviceId);
        
        executor.submit(() -> {
            if (deviceSimulator.start()) {
                System.out.println("✅ Device simulator started successfully");
            } else {
                System.out.println("❌ Failed to start device simulator");
            }
        });
    }
    
    private void stopDeviceSimulator() {
        if (deviceSimulator == null || !deviceSimulator.isRunning()) {
            System.out.println("⚠️ No device simulator is running");
            return;
        }
        
        System.out.println("🛑 Stopping device simulator...");
        deviceSimulator.stop();
        System.out.println("✅ Device simulator stopped");
    }
    
    private void showStatistics() {
        System.out.println("📊 Current Statistics:");
        System.out.println("-".repeat(30));
        
        if (testClient != null) {
            System.out.println("🔌 Test Client:");
            System.out.println("  - Connected: " + testClient.isConnected());
            System.out.println("  - Host: " + testClient.getHost());
            System.out.println("  - Port: " + testClient.getPort());
            System.out.println("  - Device IMEI: " + testClient.getDeviceImei());
            System.out.println("  - Current Serial: " + testClient.getCurrentSerialNumber());
        } else {
            System.out.println("🔌 Test Client: Not created");
        }
        
        System.out.println();
        
        if (deviceSimulator != null) {
            System.out.println("🤖 Device Simulator:");
            System.out.println("  - Running: " + deviceSimulator.isRunning());
            System.out.println("  - Logged In: " + deviceSimulator.isLoggedIn());
            System.out.println("  - Device ID: " + deviceSimulator.getDeviceId());
            System.out.println("  - Total Messages: " + deviceSimulator.getTotalMessagesSent());
            System.out.println("  - Heartbeats: " + deviceSimulator.getHeartbeatsSent());
            System.out.println("  - Locations: " + deviceSimulator.getLocationsSent());
            System.out.println("  - Alarms: " + deviceSimulator.getAlarmsSent());
        } else {
            System.out.println("🤖 Device Simulator: Not created");
        }
    }
    
    private void runQuickTest() {
        System.out.println("🧪 Running quick test...");
        
        // 创建临时客户端
        GT06TestClient quickClient = new GT06TestClient(DEFAULT_HOST, DEFAULT_PORT, "QUICK_TEST");
        
        try {
            if (quickClient.connect()) {
                System.out.println("✅ Quick test - Connection successful");
                
                if (quickClient.sendLogin()) {
                    System.out.println("✅ Quick test - Login successful");
                    
                    if (quickClient.sendHeartbeat()) {
                        System.out.println("✅ Quick test - Heartbeat successful");
                    }
                    
                    if (quickClient.sendLocation(39.9042, 116.4074, 60)) {
                        System.out.println("✅ Quick test - Location successful");
                    }
                    
                    if (quickClient.sendAlarm(AlarmMessage.AlarmType.VIBRATION)) {
                        System.out.println("✅ Quick test - Alarm successful");
                    }
                    
                    System.out.println("🎉 Quick test completed successfully!");
                } else {
                    System.out.println("❌ Quick test - Login failed");
                }
            } else {
                System.out.println("❌ Quick test - Connection failed");
            }
        } finally {
            quickClient.disconnect();
        }
    }
    
    private void disconnectClient() {
        if (testClient == null || !testClient.isConnected()) {
            System.out.println("⚠️ No client is connected");
            return;
        }
        
        System.out.println("🔌 Disconnecting client...");
        testClient.disconnect();
        System.out.println("✅ Client disconnected");
    }
    
    private boolean ensureClientConnected() {
        if (testClient == null || !testClient.isConnected()) {
            System.out.println("❌ Test client is not connected. Please connect first (option 1)");
            return false;
        }
        return true;
    }
    
    private void cleanup() {
        System.out.println("🧹 Cleaning up resources...");
        
        if (deviceSimulator != null && deviceSimulator.isRunning()) {
            deviceSimulator.stop();
        }
        
        if (testClient != null && testClient.isConnected()) {
            testClient.disconnect();
        }
        
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        
        if (scanner != null) {
            scanner.close();
        }
        
        System.out.println("✅ Cleanup completed");
    }
}
